"""
基于单目视觉的目标物测量装置 - 单文件简化版
用于测量并显示基准线到目标物的距离D（见说明）、目标物平面（简称物面）上几何图形的边长或直径x
支持圆形、等边三角形、正方形的识别和测量

重构说明：
- 将原有的多文件复杂架构重构为单文件实现
- 简化代码结构，保留核心功能
- 所有阈值调参变量集中在文件开头，便于调试优化
- 满足基础要求：距离误差≤5cm，尺寸误差≤1cm，一键启动，5秒内显示结果
"""

import time
import os
import math
import struct
from media.sensor import *
from media.display import *
from media.media import *
from machine import UART, FPIOA, Pin, Timer
import image  # K230的image模块

# ==================== 全局配置参数区域 ====================
# 所有阈值调参变量集中在此处，便于调试和优化

# 1. 摄像头和显示配置
SENSOR_WIDTH = 1280         # 传感器初始宽度
SENSOR_HEIGHT = 960         # 传感器初始高度  
FRAME_WIDTH = 640           # 输出帧宽度
FRAME_HEIGHT = 480          # 输出帧高度

# 2. A4纸检测参数
MIN_A4_AREA = 5000          # A4纸最小面积阈值(像素²) - 过滤小噪声
MAX_A4_AREA = 30000         # A4纸最大面积阈值(像素²) - 过滤过大区域
ASPECT_TOLERANCE = 0.35     # 长宽比容差 - 允许的A4纸形状偏差
A4_WIDTH_CM = 20.9          # A4纸实际宽度(厘米)
A4_HEIGHT_CM = 29.5         # A4纸实际高度(厘米)

# 3. 边缘检测参数
EDGE_LOW_THRESHOLD = 7      # 边缘检测低阈值
EDGE_HIGH_THRESHOLD = 25    # 边缘检测高阈值

# 4. 形状识别阈值参数 - 核心调参区域
BLACK_L_MAX = 50                    # LAB色彩空间L通道最大值 (调大=检测更亮的"黑色")
BLACK_A_MIN = -128                  # LAB色彩空间A通道最小值
BLACK_A_MAX = 127                   # LAB色彩空间A通道最大值  
BLACK_B_MIN = -128                  # LAB色彩空间B通道最小值
BLACK_B_MAX = 127                   # LAB色彩空间B通道最大值
PIXELS_THRESHOLD = 200              # 最小像素数阈值 (调大=过滤更多小噪声)
AREA_THRESHOLD = 500                # 最小面积阈值 (调大=过滤更多小区域)
MIN_BLOB_AREA = 1000                # 有效图形最小面积 (调大=只检测大图形)

# 5. 形状分类阈值参数 - 关键调参区域
CIRCLE_CIRCULARITY_MIN = 0.80       # 圆形最小圆形度阈值 (调大=更严格的圆形要求)
CIRCLE_RECTANGULARITY_MAX = 0.80    # 圆形最大矩形度阈值 (调大=允许更方的圆形)
SQUARE_RECTANGULARITY_MIN = 0.95    # 正方形最小矩形度阈值 (调大=更严格的矩形要求)
SQUARE_ASPECT_RATIO_MAX = 1.25      # 正方形最大长宽比阈值 (调大=允许更长的矩形)
SQUARE_CIRCULARITY_MIN = 0.60       # 正方形最小圆形度阈值 (调大=要求更圆润的角)
TRIANGLE_CIRCULARITY_MAX = 0.7      # 三角形最大圆形度阈值 (调大=允许更圆润的三角形)
TRIANGLE_RECTANGULARITY_MAX = 0.7   # 三角形最大矩形度阈值 (调大=允许更接近矩形的三角形)
FALLBACK_CIRCLE_THRESHOLD = 0.75    # 备用圆形判断阈值 (当无法明确分类时使用)

# 6. 系统配置参数
CALIBRATION_DISTANCE = 150.0        # 标定距离(cm) - 可修改为100-200cm之间的任意值
MIN_FOCAL_LENGTH = 680              # 最小焦距阈值(像素) - 避免除零错误
MAX_FOCAL_LENGTH = 800              # 最大焦距阈值(像素) - 避免异常值

# 7. 通信和控制配置
UART_BAUDRATE = 115200              # 串口波特率
TIMER_PERIOD = 30                   # 定时器周期(ms) - 数据发送频率
BUTTON_DEBOUNCE_MS = 10             # 按键消抖延时(ms)
DISTANCE_PRECISION = 10             # 距离数据精度倍数(保留1位小数)
SIZE_PRECISION = 10                 # 尺寸数据精度倍数(保留1位小数)

# ==================== 配置参数区域结束 ====================

# 全局变量
sensor = None
simple_measure = None
measurement_state = "IDLE"  # 测量状态: IDLE, CALIBRATING, MEASURING
last_measurement = None     # 最后测量结果
buf1 = b'\x00\x00\x00\x00' # 串口发送缓冲区

# 硬件初始化
fpioa = FPIOA()
fpioa.set_function(11, FPIOA.UART2_TXD)
fpioa.set_function(12, FPIOA.UART2_RXD)
button = Pin(43, Pin.IN, Pin.PULL_UP)
uart = UART(UART.UART2, baudrate=UART_BAUDRATE, bits=UART.EIGHTBITS, 
           parity=UART.PARITY_NONE, stop=UART.STOPBITS_ONE)

print("simple_measure.py 配置参数区域初始化完成")
print(f"A4纸尺寸: {A4_WIDTH_CM}cm × {A4_HEIGHT_CM}cm")
print(f"有效区域: {A4_WIDTH_CM-4.0}cm × {A4_HEIGHT_CM-4.0}cm (减去2cm边框)")
print(f"标定距离: {CALIBRATION_DISTANCE}cm")

# ==================== 简化的测量核心类 ====================

class SimpleMeasure:
    """
    简化的单目视觉测量核心类
    负责A4纸检测、形状识别、距离测量等核心功能
    去除复杂的多阈值融合等高级功能，专注核心测量逻辑
    """

    def __init__(self, a4_width_cm=A4_WIDTH_CM, a4_height_cm=A4_HEIGHT_CM):
        """
        初始化测量类

        参数:
            a4_width_cm: A4纸宽度(厘米)
            a4_height_cm: A4纸高度(厘米)
        """
        # A4纸标准尺寸参数
        self.a4_width_cm = a4_width_cm
        self.a4_height_cm = a4_height_cm

        # 计算有效区域尺寸（减去2cm边框）
        self.effective_width_cm = self.a4_width_cm - 4.0   # 有效宽度：21-4=17cm
        self.effective_height_cm = self.a4_height_cm - 4.0 # 有效高度：29.7-4=25.7cm
        self.a4_aspect_ratio = self.effective_height_cm / self.effective_width_cm  # 有效区域长宽比

        # 摄像头标定参数
        self.focal_length = None    # 摄像头等效焦距
        self.calibrated = False     # 标定状态标志

        # 测量结果缓存
        self.last_measurement = {
            'distance_cm': 0.0,
            'shape_type': '',
            'size_cm': 0.0,
            'timestamp': 0
        }

        print("SimpleMeasure初始化完成")
        print(f"有效区域长宽比: {self.a4_aspect_ratio:.3f}")

    def calibrate(self, img, known_distance_cm):
        """
        标定摄像头焦距

        参数:
            img: 输入图像，包含已知距离的A4纸
            known_distance_cm: A4纸到摄像头的真实距离(厘米)

        返回:
            bool: 标定是否成功
            float: 计算得到的焦距值
        """
        try:
            print(f"开始标定，已知距离: {known_distance_cm}cm")

            # 检测A4纸边框和尺寸信息（后续实现）
            a4_data = self._detect_a4_simple(img)
            if a4_data is None:
                print("标定失败: 未检测到A4纸")
                return False, 0.0

            # 提取A4纸在图像中的像素高度
            height_px = a4_data['height_px']
            print(f"检测到A4纸像素高度: {height_px}px")

            # 使用三角相似原理计算摄像头等效焦距
            # 焦距公式: F = (H_pixel × D_real) / H_real
            focal_length = (height_px * known_distance_cm) / self.effective_height_cm

            # 检查焦距是否在有效范围内
            if MIN_FOCAL_LENGTH <= focal_length <= MAX_FOCAL_LENGTH:
                self.focal_length = focal_length
                self.calibrated = True
                print(f"标定成功，焦距: {self.focal_length:.2f}")
                return True, self.focal_length
            else:
                print(f"标定失败: 计算的焦距值 {focal_length:.2f} 不在有效范围({MIN_FOCAL_LENGTH}-{MAX_FOCAL_LENGTH})内")
                return False, 0.0

        except Exception as e:
            print(f"标定异常: {e}")
            return False, 0.0

    def measure(self, img):
        """
        测量目标物距离和尺寸

        参数:
            img: 输入图像

        返回:
            tuple: (distance_cm, shape_type, size_cm) 或 None
        """
        try:
            # 检查标定状态
            if not self.calibrated or self.focal_length is None:
                print("测量失败: 系统未标定")
                return None

            # 检测A4纸边框和位置信息
            a4_data = self._detect_a4_simple(img)
            if a4_data is None:
                print("测量失败: 未检测到A4纸")
                return None

            # 使用三角相似原理计算目标物距离
            height_px = a4_data['height_px']
            # 距离公式: D = (H_real × F) / H_pixel
            distance_cm = (self.effective_height_cm * self.focal_length) / height_px

            # 获取A4纸ROI区域进行形状检测（后续实现）
            roi_img = self._get_a4_roi(img, a4_data['corners'])
            if roi_img is None:
                print("测量失败: ROI提取失败")
                return None

            # 在ROI图像中识别图形并计算尺寸（后续实现）
            shape_result = self._detect_shape_simple(roi_img)
            if shape_result is None:
                print("测量失败: 未检测到图形")
                return None

            shape_type, size_px = shape_result

            # 将像素尺寸转换为实际厘米尺寸
            pixel_to_cm_ratio = self.effective_width_cm / a4_data['width_px']
            size_cm = size_px * pixel_to_cm_ratio

            # 更新测量结果缓存
            timestamp = time.ticks_ms()
            self.last_measurement = {
                'distance_cm': distance_cm,
                'shape_type': shape_type,
                'size_cm': size_cm,
                'timestamp': timestamp
            }

            print(f"测量结果: 距离={distance_cm:.1f}cm, 形状={shape_type}, 尺寸={size_cm:.1f}cm")
            return distance_cm, shape_type, size_cm

        except Exception as e:
            print(f"测量异常: {e}")
            return None

    def _detect_a4_simple(self, img):
        """
        简化的A4纸检测方法（占位符，后续实现）
        """
        # 占位符实现，后续任务中完成
        print("A4纸检测方法待实现")
        return None

    def _get_a4_roi(self, img, corners):
        """
        获取A4纸ROI区域（占位符，后续实现）
        """
        # 占位符实现，后续任务中完成
        print("ROI提取方法待实现")
        return None

    def _detect_shape_simple(self, roi_img):
        """
        简化的形状检测方法（占位符，后续实现）
        """
        # 占位符实现，后续任务中完成
        print("形状检测方法待实现")
        return None

    def get_calibration_status(self):
        """获取当前标定状态信息"""
        return {
            'calibrated': self.calibrated,
            'focal_length': self.focal_length
        }

    def get_last_measurement(self):
        """获取最后一次测量结果的副本"""
        return self.last_measurement.copy()

    def reset_calibration(self):
        """重置标定状态"""
        self.focal_length = None
        self.calibrated = False
        print("标定状态已重置")
